# ChangeLog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.1.0/)

## Unreleased

## [1.0.0]

### Added

* Add admin panel functionality.
* * Implemented admin panel view for SuperAdmin and Admin users.
* * Added isAdminPanelUser field to User model and updated related routes and middleware.
* * Added checkAdminPanelView middleware to check if user is allowed to change admin panel view.
* * Created changeAdminPanelView controller to toggle admin panel access.
* * Added button in header to change admin panel view and switch back to onboarding view if user is not on onboarding step. conditionally show/hide based on user role.
* * Profile Life Data tabs are hidden if user user is on admin panel view.

### Added: Add video recording and instruction components with framer-motion integration

* Implemented VideoInstructionsModal and Instruction components for video guidance (make it reuseable).
* Created InstructionData to manage instructional content based on video type.
* Change file location of RecordVideoModal and VideoRecorder for video recording functionality.
* Integrated framer-motion for smooth transitions between recording states.
* Updated package.json and package-lock.json to include framer-motion dependency.

### Added : The recorded/uploaded video will be part of your profile and will be visible to other community members in video upload when video is selected / recorded

### Updated UI Message After Video Upload

* ### Before

* * Thanks for uploading your video We're hard at work processing them to create your personalized experience. This might take a few moments.
* * Once the video is processed and data is extracted, we will notify you via email.

* ### After

* * Thanks for uploading your video! We're processing it to create your personalized experience.
* * This usually takes 1–2 minutes. You’ll be automatically redirected to next stage once it’s ready, and we’ll also notify you by email.

### Fixed
  
* Issue[[#45](https://github.com/CQ-Dev-Team/360/issues/45)] update file upload paths to support image uploads and migrate existing video paths

### Removed
