import { MantineProvider } from "@mantine/core";
import type { MantineThemeOverride } from "@mantine/core";
import AppLayout from "./layout/AppLayout";
import { ModalsProvider } from "@mantine/modals";
import { Notifications } from "@mantine/notifications";

const theme: MantineThemeOverride = {
	primaryColor: "blue",
	defaultRadius: "sm",
	fontFamily: "Inter, sans-serif",
	white: "#fff",
	black: "#1A1B1E",
	components: {
		Button: {
			defaultProps: {
				size: "sm",
			},
			styles: {
				root: {
					fontWeight: 500,
					height: 32,
				},
			},
		},
		TextInput: {
			defaultProps: {
				size: "sm",
			},
		},
		PasswordInput: {
			defaultProps: {
				size: "sm",
			},
		},
		Select: {
			defaultProps: {
				size: "sm",
			},
		},
		Textarea: {
			defaultProps: {
				size: "sm",
			},
		},
		Paper: {
			defaultProps: {
				p: "md",
				radius: "sm",
			},
		},
		Card: {
			defaultProps: {
				padding: "md",
				radius: "sm",
			},
		},
		Badge: {
			defaultProps: {
				size: "sm",
			},
		},
		ActionIcon: {
			defaultProps: {
				size: "sm",
			},
		},
		Container: {
			defaultProps: {
				size: "xl",
			},
		},
	},
};

const App = () => {
	return (
		<MantineProvider theme={theme}>
			<ModalsProvider>
				<Notifications position="top-right" zIndex={9999} />
				<AppLayout />
			</ModalsProvider>
		</MantineProvider>
	);
};

export default App;
