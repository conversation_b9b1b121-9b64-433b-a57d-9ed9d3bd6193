import { AppShell, NavLink, Stack, TextInput, Title } from "@mantine/core";
import {
	Routes,
	Route,
	useLocation,
	useNavigate,
	matchPath,
	Navigate,
} from "react-router-dom";
import PrivateRoute from "../components/PrivateRoute";
import Login from "../pages/Login";
import { useDisclosure } from "@mantine/hooks";
import {
	IconUser,
	IconView360Number,
	IconUserCheck,
	IconSearch,
} from "@tabler/icons-react";
import { useAuth } from "../contexts/AuthContext";
import Users from "../pages/Users";
import ForgotPassword from "../pages/ForgotPassword";
import ResetPassword from "../pages/ResetPassword";
import NotFound from "../pages/NotFound";
import ProfileTabs from "../components/ProfileTabs";
import { ONBOARDING_STEP, roleValues } from "../constants";
import OnBoardingStepper from "../pages/OnBoardingStepper";
import WaitForApproval from "../components/WaitForApproval";
import ProfileReview from "../pages/ProfileReview";
import Header from "../components/Header";
import PendingApprovals from "../pages/PendingApprovals";
import { openUserSearchModal } from "../components/modals/UserSearchModal";
import SearchedUser from "../pages/SearchedUser";

function AppLayout() {
	const { user } = useAuth();
	const location = useLocation();
	const [opened, { toggle }] = useDisclosure();
	const navigate = useNavigate();

	const navItems = [
		{ label: "Profile", icon: IconView360Number, path: "/profile" },
	];

	if (user?.role !== roleValues.CommunityMember) {
		navItems.push({ label: "Users", icon: IconUser, path: "/users" });
		if (user?.canApproveUser) {
			navItems.push({
				label: "Pending Profiles",
				icon: IconUserCheck,
				path: "/pending-profiles",
			});
		}
	}

	const isPublicRoute =
		location.pathname === "/login" ||
		location.pathname === "/forgot-password" ||
		matchPath("/reset-password/:token", location.pathname);

	if (!user && isPublicRoute) {
		return (
			<Routes>
				<Route path="/login" element={<Login />} />
				<Route path="/forgot-password" element={<ForgotPassword />} />
				<Route
					path="/reset-password/:token"
					element={<ResetPassword />}
				/>
				<Route path="*" element={<Navigate to="/login" replace />} />
			</Routes>
		);
	}

	if (!user && !isPublicRoute) {
		return (
			<Routes>
				<Route path="*" element={<Navigate to="/login" replace />} />
			</Routes>
		);
	}

	if (user && isPublicRoute) {
		if (user.onboardingStep <= ONBOARDING_STEP.WAIT_FOR_APPROVAL) {
			return <Navigate to="/" replace />;
		}
		const redirectTo =
			user.role === roleValues.CommunityMember ? "/profile" : "/users";
		return <Navigate to={redirectTo} replace />;
	}

	if (
		user &&
		user.onboardingStep < ONBOARDING_STEP.WAIT_FOR_APPROVAL &&
		!user.isAdminPanelUser
	) {
		return (
			<AppShell
				layout="alt"
				header={{ height: 70 }}
				styles={{
					root: { overflow: "hidden" },
				}}
			>
				<Header />

				<AppShell.Main>
					<Routes>
						<Route path="/:step?" element={<OnBoardingStepper />} />
						<Route
							path="*"
							element={<Navigate to="/:step?" replace />}
						/>
					</Routes>
				</AppShell.Main>
			</AppShell>
		);
	}

	if (
		user &&
		user.onboardingStep === ONBOARDING_STEP.WAIT_FOR_APPROVAL &&
		!user.isAdminPanelUser
	) {
		return (
			<AppShell layout="alt" header={{ height: 70 }}>
				<Header />

				<AppShell.Main>
					<Routes>
						<Route path="/" element={<WaitForApproval />} />
						<Route path="*" element={<Navigate to="/" replace />} />
					</Routes>
				</AppShell.Main>
			</AppShell>
		);
	}

	return (
		<AppShell
			layout="alt"
			padding="md"
			header={{ height: 70 }}
			navbar={{
				width: 300,
				breakpoint: "sm",
				collapsed: { mobile: !opened },
			}}
		>
			<Header opened={opened} toggle={toggle} showTitle={false} />

			<AppShell.Navbar h="100%" p="md">
				<Stack>
					<Title order={1} fw={600}>
						360 APP
					</Title>

					<TextInput
						leftSection={<IconSearch size={16} />}
						placeholder="Search Users..."
						pointer
						onClick={e => {
							e.currentTarget.blur();
							openUserSearchModal();
						}}
					/>

					{navItems.map(item => (
						<NavLink
							key={item.path}
							label={item.label}
							leftSection={<item.icon size={16} />}
							onClick={() => navigate(item.path)}
							active={location.pathname === item.path}
						/>
					))}
				</Stack>
			</AppShell.Navbar>

			<AppShell.Main>
				<Routes>
					<Route
						path="/"
						element={
							<PrivateRoute>
								{user?.role === roleValues.CommunityMember ? (
									<Navigate to="/profile" replace />
								) : (
									<Navigate to="/users" replace />
								)}
							</PrivateRoute>
						}
					/>
					{user?.role !== 3 && (
						<>
							<Route
								path="/users"
								element={
									<PrivateRoute>
										<Users />
									</PrivateRoute>
								}
							/>
						</>
					)}
					<Route
						path="profile"
						element={
							<PrivateRoute>
								<ProfileTabs />
							</PrivateRoute>
						}
					/>
					{user?.canApproveUser && (
						<Route path="/pending-profiles">
							<Route
								index
								element={
									<PrivateRoute>
										<PendingApprovals />
									</PrivateRoute>
								}
							/>
							<Route
								path="user/:userId"
								element={
									<PrivateRoute>
										<ProfileReview />
									</PrivateRoute>
								}
							></Route>
						</Route>
					)}
					<Route
						path="search/:userId"
						element={
							<PrivateRoute>
								<SearchedUser />
							</PrivateRoute>
						}
					/>
					<Route path="*" element={<NotFound />} />
				</Routes>
			</AppShell.Main>
		</AppShell>
	);
}

export default AppLayout;
