import {
	AppShell,
	Group,
	Title,
	Text,
	Burger,
	Menu,
	Avatar,
	Box,
	MenuItem,
	Button,
} from "@mantine/core";
import openCustomModal from "./modals/CustomModal";
import { IconLogout, IconUser } from "@tabler/icons-react";
import { useMemo } from "react";
import { roleValues } from "../constants";
import { isAxiosError } from "axios";
import { notifications } from "@mantine/notifications";
import apiClient from "../config/axios";
import { useAuth } from "../contexts/AuthContext";
import { useNavigate } from "react-router-dom";

interface HeaderProps {
	opened?: boolean;
	toggle?: () => void;
	showTitle?: boolean;
}

function Header({ opened, toggle, showTitle = true }: HeaderProps) {
	const { user, fetchUser, logout } = useAuth();
	const navigate = useNavigate();

	const handleLogout = () => {
		logout();
		navigate("/login");
	};

	const canSignInAsAdmin = useMemo(() => {
		if (!user) return false;
		if (user.onboardingStepCompleted) return false;
		if (
			user.role === roleValues.SuperAdmin ||
			user.role === roleValues.Admin
		) {
			return true;
		}
		return false;
	}, [user]);

	const handleSignAsAdmin = async () => {
		if (!canSignInAsAdmin) return;
		const isAdminpanel = user && user.isAdminPanelUser;
		try {
			const response = await apiClient.post(
				"/api/users/changeAdminPanelView"
			);
			await fetchUser();
			if (isAdminpanel) {
				navigate("/");
			} else {
				navigate("/profile");
			}
			notifications.show({
				title: "Success",
				message: response.data.message,
				color: "green",
			});
		} catch (error) {
			if (isAxiosError(error)) {
				notifications.show({
					title: "Failed",
					message:
						error.response?.data?.message ??
						"Failed to sign in as admin",
					color: "red",
				});
			} else {
				notifications.show({
					title: "Failed",
					message: "Failed to sign in as admin",
					color: "red",
				});
			}
		}
	};

	return (
		<AppShell.Header p="lg">
			<Group justify="space-between">
				<Group>
					{toggle && (
						<Burger
							opened={opened}
							onClick={toggle}
							hiddenFrom="sm"
							size="sm"
						/>
					)}
					{showTitle && (
						<Title order={1} fw={600}>
							360 APP
						</Title>
					)}
				</Group>
				{user && (
					<Group gap={20}>
						{canSignInAsAdmin && (
							<Button
								onClick={() => {
									openCustomModal({
										title:
											`Are you sure you want ` +
											(user.isAdminPanelUser
												? "to exit admin panel view?"
												: "to sign in as admin?"),
										confirmCallback: () =>
											handleSignAsAdmin(),
									});
								}}
							>
								{user.isAdminPanelUser
									? "Continue Onboarding"
									: "Sign in as Admin"}
							</Button>
						)}
						<Menu
							shadow="md"
							width={240}
							position="bottom-end"
							withArrow
						>
							<Menu.Target>
								<Avatar
									color="blue"
									radius="xl"
									style={{ cursor: "pointer" }}
								>
									<IconUser size={18} />
								</Avatar>
							</Menu.Target>

							<Menu.Dropdown>
								<Box bg="gray.0" px="md" py="xs">
									<Text size="xs" c="dimmed" fw={500}>
										Signed in as
									</Text>
									<Text size="sm" fw={600} truncate>
										{user.email}
									</Text>
								</Box>

								<Menu.Divider />

								<MenuItem
									color="red"
									onClick={() => {
										openCustomModal({
											title: "Are you sure you want to logout?",
											confirmCallback: () =>
												handleLogout(),
										});
									}}
									leftSection={<IconLogout size={16} />}
									style={{
										textAlign: "left",
										fontWeight: 500,
									}}
								>
									Logout
								</MenuItem>
							</Menu.Dropdown>
						</Menu>
					</Group>
				)}
			</Group>
		</AppShell.Header>
	);
}

export default Header;
